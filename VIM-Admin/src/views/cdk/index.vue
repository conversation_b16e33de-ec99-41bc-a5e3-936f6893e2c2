<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" @submit.native.prevent>
      <el-form-item label="CDK码" prop="cdk">
        <el-input
            v-model="queryParams.cdk"
            placeholder="请输入CDK码"
            clearable
            @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="类型" prop="type">
        <el-select v-model="queryParams.type" placeholder="请选择CDK类型" clearable>
          <el-option
              v-for="dict in cdk_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="state">
        <el-select v-model="queryParams.state" placeholder="请选择CDK状态" clearable>
          <el-option
              v-for="dict in cdk_state"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮行 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['system:cdk:generate']"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="success"
            plain
            icon="Plus"
            @click="handleBatchGenerate"
            v-hasPermi="['system:cdk:batchGenerate']"
        >批量生成
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['system:cdk:delete']"
        >删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="warning"
            plain
            icon="Download"
            @click="handleExport"
            v-hasPermi="['cdk:cdk:export']"
        >导出
        </el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="cdkList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="ID" align="center" prop="id" width="80"/>
      <el-table-column label="CDK码" align="center" prop="cdk" min-width="180"/>
      <el-table-column label="类型" align="center" prop="type" width="100">
        <template #default="scope">
          <dict-tag :options="cdk_type" :value="scope.row.type"/>
        </template>
      </el-table-column>
      <el-table-column label="数量/物品ID" align="center" prop="value" width="120"/>
      <el-table-column label="绑定用户" align="center" width="150">
        <template #default="scope">
          <div v-if="scope.row.foruserNickname">
            <el-tag type="info" size="small">{{ scope.row.foruserNickname }}</el-tag>
            <div style="font-size: 12px; color: #909399; margin-top: 2px;">
              {{ scope.row.foruserPhone }}
            </div>
          </div>
          <span v-else style="color: #909399;">未绑定</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="state" width="100">
        <template #default="scope">
          <dict-tag :options="cdk_state" :value="scope.row.state"/>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="160">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="兑换时间" align="center" prop="useTime" width="160">
        <template #default="scope">
          <span>{{ scope.row.useTime ? parseTime(scope.row.useTime) : '未兑换' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:cdk:delete']">
            删除
          </el-button>
          <el-button @click="handleView(scope.row)" v-show="scope.row.state !== 0 " icon="User" link type="danger"
          v-hasPermi="['system:cdk:exchangeDetail']"
          >
            兑换详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
        v-show="total>0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
    />

    <!-- 添加或修改CDK对话框 -->
    <el-dialog :title="title" v-model="open" append-to-body>
      <el-form ref="cdkRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="CDK码" prop="cdk">
          <el-input v-model="form.cdk" placeholder="请输入CDK码，格式为xxxx-xxxx-xxxx-xxxx"/>
          <div class="input-help-text">
            可以留空自动生成，手动输入需符合xxxx-xxxx-xxxx-xxxx格式
          </div>
        </el-form-item>
        <el-form-item label="类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择CDK类型" @change="handleTypeChange">
            <el-option
                v-for="dict in cdk_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="绑定用户" prop="foruser">
          <el-select
              v-model="form.foruser"
              placeholder="请选择绑定用户"
              filterable
              remote
              reserve-keyword
              :remote-method="searchUserOptions"
              :loading="userSearchLoading"
              clearable
              style="width: 100%"
          >
            <el-option
                v-for="user in userOptions"
                :key="user.id"
                :label="`${user.nickname} (${user.phone}) - ID: ${user.id}`"
                :value="user.id"
            >
              <span style="float: left">{{ user.nickname }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ user.phone }}</span>
            </el-option>
          </el-select>
          <div class="input-help-text">
            输入用户昵称或手机号进行搜索，必须选择一个用户
          </div>
        </el-form-item>
        <el-form-item v-if="form.type !== '3'" label="数量" prop="value">
          <el-input-number v-model="form.value" :min="1"/>
          <div class="input-help-text">
            {{ getValuePlaceholder() }}
          </div>
        </el-form-item>

        <el-form-item v-if="form.type === '3'" label="选择物品" prop="value">
          <div class="item-selector">
            <!-- 搜索框 -->
            <el-input
                v-model="searchKey"
                placeholder="搜索物品名称..."
                clearable
                style="margin-bottom: 15px;"
            >
              <template #prefix>
                <el-icon>
                  <Search/>
                </el-icon>
              </template>
            </el-input>

            <!-- 物品列表 -->
            <div class="item-list">
              <template v-if="filteredItemList.length > 0">
                <div
                    v-for="item in filteredItemList"
                    :key="item.id"
                    class="item-card"
                    :class="{ 'selected': form.value === item.id }"
                    @click="selectItem(item)"
                >
                  <div class="item-image">
                    <image-preview
                        v-if="item.image"
                        :src="item.image"
                        width="60"
                        height="60"
                    />
                    <div v-else class="no-image">无图片</div>
                  </div>
                  <div class="item-info">
                    <div class="item-name">{{ item.name }}</div>
                    <div class="item-id">ID: {{ item.id }}</div>
                  </div>
                </div>
              </template>
              <div v-else class="no-items">
                没有找到匹配的物品
              </div>
            </div>
          </div>
        </el-form-item>

        <!-- 预览所选物品 -->
        <el-form-item v-if="form.type === '3' && selectedItemInfo">
          <el-divider>所选物品预览</el-divider>
          <div class="selected-item-preview">
            <div class="preview-image">
              <image-preview
                  v-if="selectedItemInfo.image"
                  :src="selectedItemInfo.image"
                  width="80"
                  height="80"
              />
              <div v-else class="no-image">无图片</div>
            </div>
            <div class="preview-info">
              <div class="preview-row">
                <span class="label">物品名称:</span>
                <span class="value">{{ selectedItemInfo.name }}</span>
              </div>
              <div class="preview-row">
                <span class="label">物品ID:</span>
                <span class="value">{{ selectedItemInfo.id }}</span>
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 批量生成CDK对话框 -->
    <el-dialog title="批量生成CDK" v-model="batchOpen" width="500px" append-to-body>
      <el-form ref="batchCdkRef" :model="batchForm" :rules="batchRules" label-width="100px">
        <el-form-item label="生成数量" prop="count">
          <el-input-number v-model="batchForm.count" :min="1" :max="1000"/>
        </el-form-item>
        <el-form-item label="类型" prop="type">
          <el-select v-model="batchForm.type" placeholder="请选择CDK类型" @change="handleBatchTypeChange">
            <el-option
                v-for="dict in cdk_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="绑定用户" prop="foruser">
          <el-select
              v-model="batchForm.foruser"
              placeholder="请选择绑定用户"
              filterable
              remote
              reserve-keyword
              :remote-method="searchBatchUserOptions"
              :loading="batchUserSearchLoading"
              clearable
              style="width: 100%"
          >
            <el-option
                v-for="user in batchUserOptions"
                :key="user.id"
                :label="`${user.nickname} (${user.phone}) - ID: ${user.id}`"
                :value="user.id"
            >
              <span style="float: left">{{ user.nickname }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ user.phone }}</span>
            </el-option>
          </el-select>
          <div class="input-help-text">
            输入用户昵称或手机号进行搜索，必须选择一个用户
          </div>
        </el-form-item>
        <el-form-item v-if="batchForm.type !== '3'" label="数量" prop="value">
          <el-input-number v-model="batchForm.value" :min="1"/>
          <div class="input-help-text">
            {{ getBatchValuePlaceholder() }}
          </div>
        </el-form-item>

        <el-form-item v-if="batchForm.type === '3'" label="选择物品" prop="value">
          <div class="item-selector">
            <!-- 搜索框 -->
            <el-input
                v-model="searchKeyBatch"
                placeholder="搜索物品名称..."
                clearable
                style="margin-bottom: 15px;"
            >
              <template #prefix>
                <el-icon>
                  <Search/>
                </el-icon>
              </template>
            </el-input>

            <!-- 物品列表 -->
            <div class="item-list">
              <template v-if="filteredItemListBatch.length > 0">
                <div
                    v-for="item in filteredItemListBatch"
                    :key="item.id"
                    class="item-card"
                    :class="{ 'selected': batchForm.value === item.id }"
                    @click="selectBatchItem(item)"
                >
                  <div class="item-image">
                    <image-preview
                        v-if="item.image"
                        :src="item.image"
                        width="60"
                        height="60"
                    />
                    <div v-else class="no-image">无图片</div>
                  </div>
                  <div class="item-info">
                    <div class="item-name">{{ item.name }}</div>
                    <div class="item-id">ID: {{ item.id }}</div>
                  </div>
                </div>
              </template>
              <div v-else class="no-items">
                没有找到匹配的物品
              </div>
            </div>
          </div>
        </el-form-item>

        <!-- 预览所选物品 -->
        <el-form-item v-if="batchForm.type === '3' && selectedBatchItemInfo">
          <el-divider>所选物品预览</el-divider>
          <div class="selected-item-preview">
            <div class="preview-image">
              <image-preview
                  v-if="selectedBatchItemInfo.image"
                  :src="selectedBatchItemInfo.image"
                  width="80"
                  height="80"
              />
              <div v-else class="no-image">无图片</div>
            </div>
            <div class="preview-info">
              <div class="preview-row">
                <span class="label">物品名称:</span>
                <span class="value">{{ selectedBatchItemInfo.name }}</span>
              </div>
              <div class="preview-row">
                <span class="label">物品ID:</span>
                <span class="value">{{ selectedBatchItemInfo.id }}</span>
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitBatchForm">确 定</el-button>
          <el-button @click="cancelBatch">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 兑换详情弹窗 -->
    <el-dialog title="CDK兑换详情" v-model="exchangeDetailOpen" width="800px" append-to-body>
      <div class="exchange-detail-header">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="CDK码">{{ currentCdk.cdk }}</el-descriptions-item>
          <el-descriptions-item label="CDK类型">{{ currentCdk.typeName }}</el-descriptions-item>
          <el-descriptions-item label="数量/物品ID">{{ currentCdk.value }}</el-descriptions-item>
          <el-descriptions-item label="状态">{{ currentCdk.stateName }}</el-descriptions-item>
        </el-descriptions>
      </div>

      <div class="exchange-detail-content" style="margin-top: 20px;">
        <h4>兑换记录</h4>
        <el-table v-loading="exchangeDetailLoading" :data="exchangeDetailList" style="width: 100%">
          <el-table-column label="用户ID" align="center" prop="userId" width="80"/>
          <el-table-column label="用户昵称" align="center" prop="nickname" width="120"/>
          <el-table-column label="手机号" align="center" prop="phone" width="120"/>
          <el-table-column label="兑换时间" align="center" prop="exchangeTime" width="160"/>
          <el-table-column label="兑换来源" align="center" prop="sourceName" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.source === 1 ? 'success' : 'warning'">
                {{ scope.row.sourceName }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>

        <!-- 兑换详情分页 -->
        <pagination
            v-show="exchangeDetailTotal > 0"
            :total="exchangeDetailTotal"
            v-model:page="exchangeDetailQueryParams.page"
            v-model:limit="exchangeDetailQueryParams.pageSize"
            @pagination="getExchangeDetailList"
        />
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="exchangeDetailOpen = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- CDK生成成功弹窗 -->
    <el-dialog
        title="CDK生成成功"
        v-model="cdkSuccessDialogVisible"
        width="800px"
        append-to-body
        :close-on-click-modal="false"
    >
      <div class="cdk-success-content">
        <div class="success-header">
          <el-icon class="success-icon" color="#67C23A" size="24">
            <SuccessFilled/>
          </el-icon>
          <span class="success-text">成功生成 {{ generatedCdkList.length }} 个CDK</span>
        </div>

        <div class="cdk-list-container">
          <el-table
              :data="generatedCdkList"
              style="width: 100%"
              max-height="400"
              border
          >
            <el-table-column label="CDK码" prop="cdk" min-width="180">
              <template #default="scope">
                <div class="cdk-code-cell">
                  <span class="cdk-code">{{ scope.row.cdk }}</span>
                  <el-button
                      type="text"
                      size="small"
                      @click="copyCdkCode(scope.row.cdk)"
                      class="copy-btn"
                  >
                    <el-icon>
                      <CopyDocument/>
                    </el-icon>
                  </el-button>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="类型" prop="typeName" width="100" align="center"/>
            <el-table-column label="数值" prop="value" width="100" align="center"/>
            <el-table-column label="状态" prop="stateName" width="100" align="center">
              <template #default="scope">
                <el-tag type="success">{{ scope.row.stateName }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="创建时间" prop="createTime" width="160" align="center"/>
          </el-table>
        </div>

        <div class="operation-buttons">
          <el-button type="primary" @click="copyAllCdkCodes">
            <el-icon>
              <CopyDocument/>
            </el-icon>
            复制所有CDK码
          </el-button>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleContinueGenerate">继续生成</el-button>
          <el-button @click="cdkSuccessDialogVisible = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Cdk">
import {getCurrentInstance, onMounted, ref, watch} from 'vue';
import {batchDelCdk, batchGenerateCdk, delCdk, generateCdk, getCdkExchangeDetail, listCdk, searchUsers} from "@/api/cdk";
import {listCommoditys} from "@/api/commoditySys/commoditys";
import {parseTime} from '@/utils/ruoyi';
import ImagePreview from "@/components/ImagePreview/index.vue";
import {CopyDocument, Search, SuccessFilled} from '@element-plus/icons-vue';
import {testCdkPagination, testPaginationParams} from '@/utils/paginationTest';
import ClipboardJS from 'clipboard';

const {proxy} = getCurrentInstance();
// 加载数据字典
const {cdk_type, cdk_state} = proxy.useDict("cdk_type", "cdk_state");

// 遮罩层
const loading = ref(false);
// 选中数组
const ids = ref([]);
// 非单个禁用
const single = ref(true);
// 非多个禁用
const multiple = ref(true);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// CDK表格数据
const cdkList = ref([]);
// 弹出层标题
const title = ref("");
// 是否显示弹出层
const open = ref(false);
// 是否显示批量生成弹出层
const batchOpen = ref(false);
// 是否显示兑换详情弹出层
const exchangeDetailOpen = ref(false);
// 是否显示CDK生成成功弹出层
const cdkSuccessDialogVisible = ref(false);
// 生成的CDK列表
const generatedCdkList = ref([]);
// 日期范围
const dateRange = ref([]);

// 兑换详情相关
const exchangeDetailLoading = ref(false);
const exchangeDetailList = ref([]);
const exchangeDetailTotal = ref(0);
const currentCdk = ref({});
const exchangeDetailQueryParams = ref({
  cdk: '',
  page: 1,
  pageSize: 10
});

// 物品列表相关
const itemList = ref([]);
const searchKey = ref('');
const filteredItemList = ref([]);
const selectedItemInfo = ref(null);

// 批量生成物品列表相关
const searchKeyBatch = ref('');
const filteredItemListBatch = ref([]);
const selectedBatchItemInfo = ref(null);

// 用户搜索相关
const userOptions = ref([]);
const userSearchLoading = ref(false);
const batchUserOptions = ref([]);
const batchUserSearchLoading = ref(false);

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  cdk: undefined,
  type: undefined,
  state: undefined,
  createTime: undefined
});

// 表单参数
const form = ref({
  id: undefined,
  cdk: undefined,
  type: undefined,
  value: 1,
  foruser: 0
});

// 批量生成表单参数
const batchForm = ref({
  count: 10,
  type: undefined,
  value: 1,
  foruser: 0
});

// CDK码格式正则验证
const cdkCodePattern = /^[A-Za-z0-9]{4}-[A-Za-z0-9]{4}-[A-Za-z0-9]{4}-[A-Za-z0-9]{4}$/;

// CDK码验证方法
const validateCdkCode = (rule, value, callback) => {
  if (!value) {
    callback(); // 允许为空
  } else if (!cdkCodePattern.test(value)) {
    callback(new Error('CDK码格式不正确，应为xxxx-xxxx-xxxx-xxxx'));
  } else {
    callback();
  }
};

// 物品选择验证方法
const validateItemSelection = (rule, value, callback) => {
  if (form.value.type === '3' && !value) {
    callback(new Error('请选择物品'));
  } else {
    callback();
  }
};

// 批量生成物品选择验证方法
const validateBatchItemSelection = (rule, value, callback) => {
  if (batchForm.value.type === '3' && !value) {
    callback(new Error('请选择物品'));
  } else {
    callback();
  }
};

// 表单校验规则
const rules = {
  cdk: [{validator: validateCdkCode, trigger: 'blur'}],
  type: [{required: true, message: "CDK类型不能为空", trigger: "change"}],
  value: [
    {required: true, message: "数量/物品ID不能为空", trigger: "change"},
    {validator: validateItemSelection, trigger: "change"}
  ]
};

// 批量生成表单校验规则
const batchRules = {
  count: [{required: true, message: "生成数量不能为空", trigger: "blur"}],
  type: [{required: true, message: "CDK类型不能为空", trigger: "change"}],
  value: [
    {required: true, message: "数量/物品ID不能为空", trigger: "change"},
    {validator: validateBatchItemSelection, trigger: "change"}
  ]
};

// 根据CDK类型获取数量/物品ID输入框提示
const getValuePlaceholder = () => {
  if (form.value.type === '1') {
    return '请输入电能数量';
  } else if (form.value.type === '2') {
    return '请输入钥匙数量';
  } else if (form.value.type === '3') {
    return '请选择物品';
  }
  return '请先选择CDK类型';
};

// 批量生成中根据CDK类型获取数量/物品ID输入框提示
const getBatchValuePlaceholder = () => {
  if (batchForm.value.type === '1') {
    return '请输入电能数量';
  } else if (batchForm.value.type === '2') {
    return '请输入钥匙数量';
  } else if (batchForm.value.type === '3') {
    return '请选择物品';
  }
  return '请先选择CDK类型';
};

// 类型变更处理
const handleTypeChange = (value) => {
  // 当类型变更为物品时，重置value
  if (value === '3') {
    form.value.value = undefined;
    selectedItemInfo.value = null;
  } else {
    form.value.value = 1;
  }
};

// 批量生成类型变更处理
const handleBatchTypeChange = (value) => {
  // 当类型变更为物品时，重置value
  if (value === '3') {
    batchForm.value.value = undefined;
    selectedBatchItemInfo.value = null;
  } else {
    batchForm.value.value = 1;
  }
};

// 获取所有商品列表
function getAllItems() {
  listCommoditys({}).then(response => {
    if (response.rows) {
      itemList.value = response.rows;
      filteredItemList.value = response.rows;
      filteredItemListBatch.value = response.rows;
    }
  });
}

// 根据关键字过滤物品列表
function filterItems() {
  if (!searchKey.value) {
    filteredItemList.value = itemList.value;
    return;
  }

  filteredItemList.value = itemList.value.filter(item =>
      item.name && item.name.toLowerCase().includes(searchKey.value.toLowerCase())
  );
}

// 根据关键字过滤批量生成物品列表
function filterBatchItems() {
  if (!searchKeyBatch.value) {
    filteredItemListBatch.value = itemList.value;
    return;
  }

  filteredItemListBatch.value = itemList.value.filter(item =>
      item.name && item.name.toLowerCase().includes(searchKeyBatch.value.toLowerCase())
  );
}

// 监听搜索关键字变化
watch(searchKey, () => {
  filterItems();
});

// 监听批量生成搜索关键字变化
watch(searchKeyBatch, () => {
  filterBatchItems();
});

// 选择物品
function selectItem(item) {
  form.value.value = item.id;
  selectedItemInfo.value = item;
}

// 选择批量生成物品
function selectBatchItem(item) {
  batchForm.value.value = item.id;
  selectedBatchItemInfo.value = item;
}

// 获取CDK列表
function getList() {
  loading.value = true;

  // 测试分页参数
  testPaginationParams(queryParams.value, 'CDK管理');

  const requestParams = proxy.addDateRange(queryParams.value, dateRange.value);

  listCdk(requestParams).then(response => {
    cdkList.value = response.rows || [];
    total.value = response.total || 0;
    loading.value = false;
  }).catch(error => {
    cdkList.value = [];
    total.value = 0;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 取消批量生成
function cancelBatch() {
  batchOpen.value = false;
  resetBatch();
}

// 表单重置
function reset() {
  form.value = {
    id: undefined,
    cdk: undefined,
    type: undefined,
    value: 1,
    foruser: 0
  };
  selectedItemInfo.value = null;
  searchKey.value = '';
  userOptions.value = [];
  proxy.resetForm("cdkRef");
}

// 批量生成表单重置
function resetBatch() {
  batchForm.value = {
    count: 10,
    type: undefined,
    value: 1,
    foruser: 0
  };
  selectedBatchItemInfo.value = null;
  searchKeyBatch.value = '';
  batchUserOptions.value = [];
  proxy.resetForm("batchCdkRef");
}

// 搜索按钮操作
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

// 重置按钮操作
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryRef");
  queryParams.value.pageNum = 1;
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

// 新增按钮操作
function handleAdd() {
  reset();
  open.value = true;
  title.value = "新增CDK";
}

// 批量生成按钮操作
function handleBatchGenerate() {
  resetBatch();
  batchOpen.value = true;
}

// 提交按钮
function submitForm() {
  proxy.$refs["cdkRef"].validate(valid => {
    if (valid) {
      generateCdk(form.value).then(response => {
        proxy.$modal.msgSuccess("新增成功");
        open.value = false;

        // 显示生成成功弹窗
        if (response.data) {
          generatedCdkList.value = [response.data];
          cdkSuccessDialogVisible.value = true;
        }

        getList();
      });
    }
  });
}

// 提交批量生成
function submitBatchForm() {
  proxy.$refs["batchCdkRef"].validate(valid => {
    if (valid) {
      batchGenerateCdk(batchForm.value).then(response => {
        proxy.$modal.msgSuccess(`成功生成${batchForm.value.count}个CDK`);
        batchOpen.value = false;

        // 显示生成成功弹窗
        if (response.data && Array.isArray(response.data)) {
          generatedCdkList.value = response.data;
          cdkSuccessDialogVisible.value = true;
        }

        getList();
      });
    }
  });
}

// 查看兑换详情
function handleView(row) {
  currentCdk.value = {...row};
  exchangeDetailQueryParams.value.cdk = row.cdk;
  exchangeDetailQueryParams.value.page = 1;
  exchangeDetailOpen.value = true;
  getExchangeDetailList();
}

// 获取兑换详情列表
function getExchangeDetailList() {
  exchangeDetailLoading.value = true;
  getCdkExchangeDetail(exchangeDetailQueryParams.value).then(response => {
    exchangeDetailList.value = response.rows || [];
    exchangeDetailTotal.value = response.total || 0;
    exchangeDetailLoading.value = false;
  }).catch(error => {
    exchangeDetailList.value = [];
    exchangeDetailTotal.value = 0;
    exchangeDetailLoading.value = false;
    proxy.$modal.msgError('获取兑换详情失败');
  });
}


// 删除按钮操作
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除CDK编号为"' + _ids + '"的数据项？').then(function () {
    if (Array.isArray(_ids)) {
      return batchDelCdk(_ids);
    } else {
      return delCdk(_ids);
    }
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {
  });
}

// 导出按钮操作
function handleExport() {
  proxy.download('cdk/export', {
    ...queryParams.value
  }, `cdk_${new Date().getTime()}.xlsx`)
}

// CDK生成成功弹窗相关方法
// 复制单个CDK码（使用clipboard.js）
function copyCdkCode(cdkCode) {
  try {
    // 使用clipboard.js的静态方法直接复制文本
    const success = ClipboardJS.copy(cdkCode);
    if (success) {
      proxy.$modal.msgSuccess(`CDK码 ${cdkCode} 已复制到剪贴板`);
    } else {
      throw new Error('clipboard.js复制返回false');
    }
  } catch (err) {
    console.error('clipboard.js复制失败:', err);
    // 降级到传统方案
    fallbackCopyToClipboard(cdkCode, `CDK码 ${cdkCode} 已复制到剪贴板`);
  }
}

// 复制所有CDK码（使用clipboard.js）
function copyAllCdkCodes() {
  const allCdkCodes = generatedCdkList.value.map(item => item.cdk).join('\n');

  try {
    // 使用clipboard.js的静态方法直接复制文本
    const success = ClipboardJS.copy(allCdkCodes);
    if (success) {
      proxy.$modal.msgSuccess(`已复制 ${generatedCdkList.value.length} 个CDK码到剪贴板`);
    } else {
      throw new Error('clipboard.js复制返回false');
    }
  } catch (err) {
    console.error('clipboard.js复制失败:', err);
    // 降级到传统方案
    fallbackCopyToClipboard(allCdkCodes, `已复制 ${generatedCdkList.value.length} 个CDK码到剪贴板`);
  }
}

// 降级复制方案（仅在clipboard.js失败时使用）
function fallbackCopyToClipboard(text, successMessage) {
  try {
    // 使用现代Clipboard API
    if (navigator.clipboard && window.isSecureContext) {
      navigator.clipboard.writeText(text).then(() => {
        proxy.$modal.msgSuccess(successMessage);
      }).catch(() => {
        // 最后的降级方案
        legacyCopyToClipboard(text, successMessage);
      });
    } else {
      // 直接使用传统方案
      legacyCopyToClipboard(text, successMessage);
    }
  } catch (err) {
    console.error('降级复制方案失败:', err);
    proxy.$modal.msgError('复制失败，请手动复制内容');
  }
}

// 传统复制方案（最后的降级选择）
function legacyCopyToClipboard(text, successMessage) {
  try {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    const successful = document.execCommand('copy');
    document.body.removeChild(textArea);

    if (successful) {
      proxy.$modal.msgSuccess(successMessage);
    } else {
      throw new Error('execCommand复制失败');
    }
  } catch (err) {
    console.error('传统复制方案失败:', err);
    proxy.$modal.msgError('复制失败，请手动复制内容');
  }
}

// 导出生成的CDK列表
function exportGeneratedCdks() {
  if (generatedCdkList.value.length === 0) {
    proxy.$modal.msgWarning('没有可导出的CDK数据');
    return;
  }

  // 构造导出参数，只导出生成的CDK
  const cdkCodes = generatedCdkList.value.map(item => item.cdk);
  const exportParams = {
    cdkCodes: cdkCodes.join(','), // 传递CDK码列表
    pageNum: 1,
    pageSize: 1000
  };

  // 使用POST方法导出
  proxy.download('cdk/export', exportParams, `generated_cdk_${new Date().getTime()}.xlsx`);

  proxy.$modal.msgSuccess('CDK列表导出成功');
}

// 继续生成CDK
function handleContinueGenerate() {
  cdkSuccessDialogVisible.value = false;
  // 根据上次生成的类型决定打开哪个对话框
  if (generatedCdkList.value.length === 1) {
    // 单个生成
    handleAdd();
  } else {
    // 批量生成
    handleBatchGenerate();
  }
}

// 搜索用户选项（单个生成）
function searchUserOptions(keyword) {
  if (!keyword) {
    userOptions.value = [];
    return;
  }

  userSearchLoading.value = true;
  searchUsers({
    keyword: keyword,
    page: 1,
    pageSize: 20
  }).then(response => {
    userOptions.value = response.rows || [];
    userSearchLoading.value = false;
  }).catch(error => {
    console.error('搜索用户失败:', error);
    userOptions.value = [];
    userSearchLoading.value = false;
    proxy.$modal.msgError('搜索用户失败');
  });
}

// 搜索用户选项（批量生成）
function searchBatchUserOptions(keyword) {
  if (!keyword) {
    batchUserOptions.value = [];
    return;
  }

  batchUserSearchLoading.value = true;
  searchUsers({
    keyword: keyword,
    page: 1,
    pageSize: 20
  }).then(response => {
    batchUserOptions.value = response.rows || [];
    batchUserSearchLoading.value = false;
  }).catch(error => {
    console.error('搜索用户失败:', error);
    batchUserOptions.value = [];
    batchUserSearchLoading.value = false;
    proxy.$modal.msgError('搜索用户失败');
  });
}

// 在页面加载时获取列表数据和商品列表
onMounted(() => {
  getList();
  getAllItems();
});
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.input-help-text {
  font-size: 12px;
  color: #909399;
  line-height: 1.2;
  padding-top: 4px;
}

/* 物品选择器样式 */
.item-selector {
  width: 100%;
}

.item-list {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 10px;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.item-card {
  width: calc(50% - 10px);
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 10px;
  display: flex;
  cursor: pointer;
  transition: all 0.3s;
  margin-bottom: 5px;
}

.item-card:hover {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.item-card.selected {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.item-image {
  min-width: 60px;
  width: 60px;
  height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 15px;
}

.no-image {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
  color: #909399;
  font-size: 12px;
}

.item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  overflow: hidden;
}

.item-name {
  font-weight: bold;
  font-size: 14px;
  line-height: 1.4;
  margin-bottom: 5px;
  word-break: break-all;
  word-wrap: break-word;
  white-space: normal;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.item-id {
  font-size: 12px;
  color: #606266;
}

.no-items {
  width: 100%;
  padding: 20px;
  text-align: center;
  color: #909399;
}

.selected-item-preview {
  display: flex;
  align-items: center;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.preview-image {
  margin-right: 15px;
}

.preview-info {
  flex: 1;
}

.preview-row {
  display: flex;
  margin-bottom: 8px;
}

.preview-row .label {
  font-weight: bold;
  width: 80px;
}

.preview-row .value {
  flex: 1;
}

/* CDK生成成功弹窗样式 */
.cdk-success-content {
  padding: 10px 0;
}

.success-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f0f9ff;
  border-radius: 8px;
  border-left: 4px solid #67C23A;
}

.success-icon {
  margin-right: 10px;
}

.success-text {
  font-size: 16px;
  font-weight: 600;
  color: #67C23A;
}

.cdk-list-container {
  margin-bottom: 20px;
  border-radius: 8px;
  overflow: hidden;
}

.cdk-code-cell {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.cdk-code {
  font-family: 'Courier New', monospace;
  font-weight: 600;
  color: #2c3e50;
  letter-spacing: 1px;
}

.copy-btn {
  margin-left: 8px;
  padding: 4px 8px;
  color: #409EFF;
}

.copy-btn:hover {
  background-color: #ecf5ff;
  border-radius: 4px;
}

.operation-buttons {
  display: flex;
  justify-content: center;
  gap: 15px;
  padding: 15px;
  background-color: #fafafa;
  border-radius: 8px;
}

.operation-buttons .el-button {
  padding: 10px 20px;
  font-weight: 500;
}
</style> 