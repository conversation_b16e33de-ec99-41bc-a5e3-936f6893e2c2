<template>
  <div class="sidebar-logo-container" :class="{ 'collapse': collapse }" :style="{ backgroundColor: sideTheme === 'theme-dark' ? variables.menuBackground : variables.menuLightBackground }">
    <transition name="sidebarLogoFade">
      <router-link v-if="collapse" key="collapse" class="sidebar-logo-link" to="/">
        <img v-if="logo" :src="logo" class="sidebar-logo" />
        <div v-else class="sidebar-title-container">
          <h1 class="sidebar-title" :style="{ color: sideTheme === 'theme-dark' ? variables.logoTitleColor : variables.logoLightTitleColor }">{{ title }}</h1>
        </div>
      </router-link>
      <router-link v-else key="expand" class="sidebar-logo-link" to="/">
        <img v-if="logo" :src="logo" class="sidebar-logo" />
        <div class="sidebar-title-container">
          <h1 class="sidebar-title" :style="{ color: sideTheme === 'theme-dark' ? variables.logoTitleColor : variables.logoLightTitleColor }">{{ title }}</h1>
          <p class="sidebar-subtitle" :style="{ color: sideTheme === 'theme-dark' ? variables.logoTitleColor : variables.logoLightTitleColor }">{{ subtitle1 }}</p>
          <p class="sidebar-subtitle" :style="{ color: sideTheme === 'theme-dark' ? variables.logoTitleColor : variables.logoLightTitleColor }">
            <img src="@/assets/images/police-Deb_aa6E.png" alt="公安备案" class="police-logo" />
            {{ subtitle2 }}</p>
          <p class="sidebar-subtitle" :style="{ color: sideTheme === 'theme-dark' ? variables.logoTitleColor : variables.logoLightTitleColor }">
            {{ subtitle3 }}</p>
        </div>
      </router-link>
    </transition>
  </div>
</template>

<script setup>
import variables from '@/assets/styles/variables.module.scss'
import logo from '@/assets/logo/logo.svg'
import useSettingsStore from '@/store/modules/settings'

defineProps({
  collapse: {
    type: Boolean,
    required: true
  }
})

const title = import.meta.env.VITE_APP_TITLE;
const subtitle1 = '邯郸市兆宁网络科技有限公司';
const subtitle2 = '冀公网安备13040302001687号 ';
const subtitle3 = '冀ICP备2024097556号-6';
const settingsStore = useSettingsStore();
const sideTheme = computed(() => settingsStore.sideTheme);
</script>

<style lang="scss" scoped>
.sidebarLogoFade-enter-active {
  transition: opacity 1.5s;
}

.sidebarLogoFade-enter,
.sidebarLogoFade-leave-to {
  opacity: 0;
}

.sidebar-logo-container {
  position: relative;
  width: 100%;
  height: 120px; // 增加高度以容纳副标题
  background: #2b2f3a;
  text-align: center;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;

  & .sidebar-logo-link {
    height: 100%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    & .sidebar-logo {
      width: 28px;
      height: 32px;
      vertical-align: middle;
      margin-right: 12px;
    }

    & .sidebar-title-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      line-height: 1.2;
    }

    & .sidebar-title {
      display: block;
      margin: 0;
      color: #fff;
      font-weight: 600;
      font-size: 14px;
      font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
      line-height: 1.2;
    }

    & .sidebar-subtitle {
      display: block;
      margin: 2px 0 0 0;
      color: #fff;
      font-weight: 400;
      font-size: 11px;
      font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
      opacity: 0.8;
      line-height: 1.2;
    }
  }

  &.collapse {
    height: 50px; // 折叠时保持原有高度

    .sidebar-logo {
      margin-right: 0px;
    }

    .sidebar-title-container {
      .sidebar-title {
        line-height: 50px;
      }

      .sidebar-subtitle {
        display: none; // 折叠时隐藏副标题
      }
    }
  }

  .police-logo{
    width: 18px;
    height: 18px;
  }
}
</style>