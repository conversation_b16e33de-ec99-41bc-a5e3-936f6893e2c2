package com.ruoyi.project.permissionAudit.utils;

import com.ruoyi.project.permissionAudit.domain.MenuHierarchy;
import com.ruoyi.project.permissionAudit.domain.PermissionInfo;
import com.ruoyi.project.permissionAudit.domain.PermissionSource;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 权限标识解析工具类
 * 用于解析和分析权限标识符
 * 
 * <AUTHOR>
 */
public class PermissionParser {
    
    /**
     * 解析权限标识符，提取模块、实体、操作信息
     * 
     * @param permission 权限标识符 (如: system:user:list)
     * @return 权限解析结果
     */
    public static PermissionParts parsePermission(String permission) {
        if (permission == null || permission.trim().isEmpty()) {
            return null;
        }
        
        String[] parts = permission.trim().split(":");
        if (parts.length != 3) {
            return null;
        }
        
        return new PermissionParts(parts[0], parts[1], parts[2]);
    }
    
    /**
     * 根据权限标识符推断菜单类型
     * 
     * @param permission 权限标识符
     * @return 菜单类型 (M=目录, C=菜单, F=按钮)
     */
    public static String inferMenuType(String permission) {
        PermissionParts parts = parsePermission(permission);
        if (parts == null) {
            return "F"; // 默认为按钮
        }
        
        String action = parts.getAction().toLowerCase();
        
        // 根据操作类型推断菜单类型
        switch (action) {
            case "list":
            case "query":
            case "view":
                return "C"; // 菜单
            case "add":
            case "edit":
            case "remove":
            case "delete":
            case "update":
            case "export":
            case "import":
            case "reset":
                return "F"; // 按钮
            default:
                return "F"; // 默认为按钮
        }
    }
    
    /**
     * 根据权限标识符生成菜单名称
     * 
     * @param permission 权限标识符
     * @return 菜单名称
     */
    public static String generateMenuName(String permission) {
        PermissionParts parts = parsePermission(permission);
        if (parts == null) {
            return permission;
        }
        
        String module = translateModule(parts.getModule());
        String entity = translateEntity(parts.getEntity());
        String action = translateAction(parts.getAction());
        
        String menuType = inferMenuType(permission);
        if ("C".equals(menuType)) {
            return entity + "管理";
        } else {
            return action + entity;
        }
    }
    
    /**
     * 构建权限层级结构
     * 
     * @param permissions 权限列表
     * @return 菜单层级映射
     */
    public static Map<String, MenuHierarchy> buildPermissionHierarchy(List<String> permissions) {
        Map<String, MenuHierarchy> hierarchyMap = new HashMap<>();
        Map<String, MenuHierarchy> moduleMap = new HashMap<>();
        Map<String, MenuHierarchy> entityMap = new HashMap<>();
        
        for (String permission : permissions) {
            PermissionParts parts = parsePermission(permission);
            if (parts == null) {
                continue;
            }
            
            String moduleKey = parts.getModule();
            String entityKey = parts.getModule() + ":" + parts.getEntity();
            
            // 创建或获取模块级菜单
            MenuHierarchy moduleMenu = moduleMap.get(moduleKey);
            if (moduleMenu == null) {
                moduleMenu = new MenuHierarchy();
                moduleMenu.setMenuName(translateModule(parts.getModule()) + "管理");
                moduleMenu.setMenuType("M");
                moduleMenu.setPermission("");
                moduleMenu.setParentId(0L);
                moduleMap.put(moduleKey, moduleMenu);
                hierarchyMap.put(moduleKey, moduleMenu);
            }
            
            // 创建或获取实体级菜单
            MenuHierarchy entityMenu = entityMap.get(entityKey);
            if (entityMenu == null) {
                entityMenu = new MenuHierarchy();
                entityMenu.setMenuName(translateEntity(parts.getEntity()) + "管理");
                entityMenu.setMenuType("C");
                entityMenu.setPermission(parts.getModule() + ":" + parts.getEntity() + ":list");
                moduleMenu.addChild(entityMenu);
                entityMap.put(entityKey, entityMenu);
                hierarchyMap.put(entityKey, entityMenu);
            }
            
            // 创建操作级按钮
            if (!"list".equals(parts.getAction())) {
                MenuHierarchy actionMenu = new MenuHierarchy();
                actionMenu.setMenuName(generateMenuName(permission));
                actionMenu.setMenuType("F");
                actionMenu.setPermission(permission);
                entityMenu.addChild(actionMenu);
                hierarchyMap.put(permission, actionMenu);
            }
        }
        
        return hierarchyMap;
    }
    
    /**
     * 生成下一个可用的菜单ID
     * 
     * @param existingMenus 现有菜单列表
     * @return 下一个菜单ID
     */
    public static Long generateNextMenuId(List<MenuHierarchy> existingMenus) {
        Long maxId = 0L;
        for (MenuHierarchy menu : existingMenus) {
            if (menu.getMenuId() != null && menu.getMenuId() > maxId) {
                maxId = menu.getMenuId();
            }
        }
        return maxId + 1;
    }
    
    /**
     * 翻译模块名称
     */
    private static String translateModule(String module) {
        Map<String, String> moduleMap = new HashMap<>();
        moduleMap.put("system", "系统");
        moduleMap.put("vim", "VIM");
        moduleMap.put("monitor", "监控");
        moduleMap.put("tool", "工具");
        moduleMap.put("commodity", "商品");
        moduleMap.put("order", "订单");
        moduleMap.put("user", "用户");
        moduleMap.put("box", "宝箱");
        moduleMap.put("promotion", "促销");
        moduleMap.put("price", "价格");
        
        return moduleMap.getOrDefault(module.toLowerCase(), module);
    }
    
    /**
     * 翻译实体名称
     */
    private static String translateEntity(String entity) {
        Map<String, String> entityMap = new HashMap<>();
        entityMap.put("user", "用户");
        entityMap.put("role", "角色");
        entityMap.put("menu", "菜单");
        entityMap.put("dept", "部门");
        entityMap.put("post", "岗位");
        entityMap.put("dict", "字典");
        entityMap.put("config", "配置");
        entityMap.put("notice", "通知");
        entityMap.put("log", "日志");
        entityMap.put("job", "任务");
        entityMap.put("commodity", "商品");
        entityMap.put("order", "订单");
        entityMap.put("box", "宝箱");
        entityMap.put("item", "物品");
        entityMap.put("price", "价格");
        
        return entityMap.getOrDefault(entity.toLowerCase(), entity);
    }
    
    /**
     * 翻译操作名称
     */
    private static String translateAction(String action) {
        Map<String, String> actionMap = new HashMap<>();
        actionMap.put("list", "查询");
        actionMap.put("query", "查询");
        actionMap.put("view", "查看");
        actionMap.put("add", "新增");
        actionMap.put("edit", "修改");
        actionMap.put("update", "修改");
        actionMap.put("remove", "删除");
        actionMap.put("delete", "删除");
        actionMap.put("export", "导出");
        actionMap.put("import", "导入");
        actionMap.put("reset", "重置");
        actionMap.put("status", "状态修改");
        
        return actionMap.getOrDefault(action.toLowerCase(), action);
    }
    
    /**
     * 权限解析结果内部类
     */
    public static class PermissionParts {
        private String module;
        private String entity;
        private String action;
        
        public PermissionParts(String module, String entity, String action) {
            this.module = module;
            this.entity = entity;
            this.action = action;
        }
        
        public String getModule() {
            return module;
        }
        
        public String getEntity() {
            return entity;
        }
        
        public String getAction() {
            return action;
        }
        
        public String getFullPermission() {
            return module + ":" + entity + ":" + action;
        }
        
        @Override
        public String toString() {
            return "PermissionParts{" +
                    "module='" + module + '\'' +
                    ", entity='" + entity + '\'' +
                    ", action='" + action + '\'' +
                    '}';
        }
    }
}