package com.ruoyi.project.vimCdkSys.domain;

import lombok.Data;

import java.math.BigDecimal;

/**
 * CDK实体类
 */
@Data
public class VimCdk {
    /**
     * 主键ID
     */
    private Integer id;
    
    /**
     * CDK码
     */
    private String cdk;
    
    /**
     * 类型（1：电能 2：钥匙 3：物品）
     */
    private Integer type;
    
    /**
     * 数量或物品ID
     */
    private BigDecimal value;
    
    /**
     * 状态（0：未兑换 1：已兑换）
     */
    private Integer state;
    
    /**
     * 创建时间
     */
    private Integer createTime;
    
    /**
     * 兑换时间
     */
    private Integer useTime;

    /**
     * 兑换用户ID（vim_user表的ID）
     */
    private Integer useUser = 0;

    /**
     * 指定兑换用户ID（vim_user表的ID）
     */
    private Integer foruser;
}